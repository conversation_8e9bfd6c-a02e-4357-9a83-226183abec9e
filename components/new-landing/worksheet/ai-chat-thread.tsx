'use client';

import { useEffect, useRef } from 'react';

interface ChatMessage {
  role: 'user' | 'ai';
  content: string;
}

interface AiChatThreadProps {
  messages: ChatMessage[];
}

export default function AiChatThread({ messages }: AiChatThreadProps) {
  const bottomRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <aside className="w-[35%] min-w-[320px] h-screen overflow-y-auto bg-black text-white flex flex-col border-r border-white/10 p-4 sticky top-0">
      <h2 className="text-lg font-semibold mb-4">AI Thread</h2>
      <div className="space-y-4 flex-1 overflow-y-auto">
        {messages.map((msg, idx) => (
          <div
            key={idx}
            className={`rounded-xl px-4 py-2 text-sm whitespace-pre-wrap max-w-[90%] ${
              msg.role === 'user'
                ? 'ml-auto bg-white text-black'
                : 'mr-auto bg-gray-800 text-white'
            }`}
          >
            {msg.content}
          </div>
        ))}
        <div ref={bottomRef} />
      </div>
    </aside>
  );
}
